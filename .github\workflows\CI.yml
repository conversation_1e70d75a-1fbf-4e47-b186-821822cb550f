name: CI

on:
  push:
    paths-ignore:
      - '**.md'

  pull_request:
    paths-ignore:
      - '**.md'

jobs:
  build-linux:
    name: build-linux
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: build
        run: |
          sudo apt update
          sudo apt install libssl-dev libnghttp2-dev
          ./configure --with-openssl --with-nghttp2 --with-kcp --with-mqtt
          make libhv evpp

      - name: test
        run: |
          make check
          make run-unittest

  build-windows:
    name: build-windows
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v3
      - name: build
        run: |
          mkdir cmake-build-win64
          cd cmake-build-win64
          cmake .. -G "Visual Studio 17 2022" -A x64 -DCMAKE_BUILD_TYPE=Release
          cmake --build . --config Release
          cd ..

      - name: test
        run: |
          cd cmake-build-win64
          start bin/Release/http_server_test
          bin/Release/http_client_test
          bin/Release/curl -v -X HEAD http://example.com/
          bin/Release/curl -v -X HEAD https://example.com/
          bin/Release/wrk -c 100 -t 2 -d 10s http://127.0.0.1:8080/ping

  build-mac:
    name: build-mac
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v3
      - name: build
        run: |
          ./configure
          make libhv evpp examples unittest

      - name: test
        run: |
          bin/curl -v -X HEAD http://example.com/
          bin/curl -v -X HEAD https://example.com/

  build-android:
    name: build-android
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: build
        run: |
          mkdir cmake-build-arm64
          cd cmake-build-arm64
          cmake .. -DCMAKE_TOOLCHAIN_FILE="$ANDROID_NDK_ROOT/build/cmake/android.toolchain.cmake" -DANDROID_ABI="arm64-v8a" -DANDROID_PLATFORM=android-21
          cmake --build . --target hv --config Release

  build-ios:
    name: build-ios
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v3
      - name: build
        run: |
          mkdir cmake-build-ios
          cd cmake-build-ios
          cmake .. -G Xcode -DCMAKE_TOOLCHAIN_FILE=../cmake/ios.toolchain.cmake -DPLATFORM=OS64 -DARCHS="arm64" -DDEPLOYMENT_TARGET=9.0
          cmake --build . --target hv_static --config Release
