MKDIR = -mkdir -p 2>/dev/null
CP = -cp -r 2>/dev/null
RM = -rm -r 2>/dev/null
LDCONFIG = -ldconfig 2>/dev/null

PREFIX ?= /usr/local
INSTALL_INCDIR ?= $(PREFIX)/include/hv
INSTALL_LIBDIR ?= $(PREFIX)/lib

BASE_HEADERS =  base/hplatform.h\
				\
				base/hdef.h\
				base/hatomic.h\
				base/herr.h\
				base/htime.h\
				base/hmath.h\
				base/hbase.h\
				base/hversion.h\
				base/hsysinfo.h\
				base/hproc.h\
				base/hthread.h\
				base/hmutex.h\
				base/hsocket.h\
				base/hlog.h\
				base/hbuf.h\
				base/hmain.h\
				base/hendian.h\

SSL_HEADERS =   ssl/hssl.h

EVENT_HEADERS = event/hloop.h\
				event/nlog.h\

UTIL_HEADERS =  util/base64.h\
				util/md5.h\
				util/sha1.h\

CPPUTIL_HEADERS = cpputil/hmap.h\
				cpputil/hstring.h\
				cpputil/hfile.h\
				cpputil/hpath.h\
				cpputil/hdir.h\
				cpputil/hurl.h\
				cpputil/hscope.h\
				cpputil/hthreadpool.h\
				cpputil/hasync.h\
				cpputil/hobjectpool.h\
				cpputil/ifconfig.h\
				cpputil/iniparser.h\
				cpputil/json.hpp\
				cpputil/singleton.h\
				cpputil/ThreadLocalStorage.h\

EVPP_HEADERS  = evpp/Buffer.h\
				evpp/Channel.h\
				evpp/Event.h\
				evpp/EventLoop.h\
				evpp/EventLoopThread.h\
				evpp/EventLoopThreadPool.h\
				evpp/Status.h\
				evpp/TcpClient.h\
				evpp/TcpServer.h\
				evpp/UdpClient.h\
				evpp/UdpServer.h\

PROTOCOL_HEADERS =  protocol/icmp.h\
					protocol/dns.h\
					protocol/ftp.h\
					protocol/smtp.h

HTTP_HEADERS =  http/httpdef.h\
				http/wsdef.h\
				http/http_content.h\
				http/HttpMessage.h\
				http/HttpParser.h\
				http/WebSocketParser.h\
				http/WebSocketChannel.h\

HTTP2_HEADERS = http/http2def.h\
				http/grpcdef.h\

HTTP_CLIENT_HEADERS =   http/client/HttpClient.h\
						http/client/requests.h\
						http/client/axios.h\
						http/client/AsyncHttpClient.h\
						http/client/WebSocketClient.h\

HTTP_SERVER_HEADERS =   http/server/HttpServer.h\
						http/server/HttpService.h\
						http/server/HttpContext.h\
						http/server/HttpResponseWriter.h\
						http/server/WebSocketServer.h\

MQTT_HEADERS = mqtt/mqtt_protocol.h\
			   mqtt/mqtt_client.h\
