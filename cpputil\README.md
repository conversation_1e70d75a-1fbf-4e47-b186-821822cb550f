## 目录结构

```
.
├── hasync.h        hv::async实现
├── hdir.h          目录(ls实现)
├── hfile.h         文件类
├── hobjectpool.h   对象池
├── hpath.h         路径操作
├── hscope.h        作用域模板类
├── hstring.h       字符串操作
├── hthreadpool.h   线程池
├── hurl.h          URL操作
├── ifconfig.h      网络配置(ifconfig实现)
├── iniparser.h     INI解析
├── json.hpp        JSON解析
├── singleton.h     单例模式宏
└── ThreadLocalStorage.h 线程本地存储类

```
