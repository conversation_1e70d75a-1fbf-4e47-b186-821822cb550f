# cd libhv
# sudo nginx -p . -c etc/nginx.conf
# bin/httpd -c etc/httpd.conf -s restart -d
# bin/curl -v http://127.0.0.1/api/v1/get

worker_processes    auto;

pid         logs/nginx.pid;
error_log   logs/error.log;

events {
    worker_connections  1024;
}

http {
    access_log      logs/access.log;

    server {
        listen      80;

        # static files service
        location / {
            root    html;
            index   index.html;
        }

        # autoindex service
        location /downloads/ {
            autoindex   on;
        }

        # api service: nginx => libhv
        location /api/v1/ {
            proxy_pass  http://127.0.0.1:8080/;
        }
    }
}
